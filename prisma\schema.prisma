// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  walletAddress String @unique
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  stakingPositions StakingPosition[]
  depositAddresses DepositAddress[]
  transactions     Transaction[]
  balances         UserBalance[]

  @@map("users")
}

model Token {
  id       String @id @default(auto()) @map("_id") @db.ObjectId
  symbol   String @unique
  name     String
  logoUrl  String?
  decimals Int    @default(9)
  mintAddress String @unique

  stakingPositions StakingPosition[]
  stakingPools     StakingPool[]

  @@map("tokens")
}

model StakingPool {
  id          String @id @default(auto()) @map("_id") @db.ObjectId
  tokenId     String @db.ObjectId
  name        String
  apy         Float
  totalStaked Float  @default(0)
  maxStake    Float?
  minStake    Float  @default(0)
  isActive    Boolean @default(true)

  token            Token             @relation(fields: [tokenId], references: [id])
  stakingPositions StakingPosition[]

  @@map("staking_pools")
}

model StakingPosition {
  id            String   @id @default(auto()) @map("_id") @db.ObjectId
  userId        String   @db.ObjectId
  tokenId       String   @db.ObjectId
  poolId        String   @db.ObjectId
  amount        Float
  stakedAt      DateTime @default(now())
  lastRewardAt  DateTime @default(now())
  isActive      Boolean  @default(true)

  user  User        @relation(fields: [userId], references: [id])
  token Token       @relation(fields: [tokenId], references: [id])
  pool  StakingPool @relation(fields: [poolId], references: [id])

  @@map("staking_positions")
}

model Protocol {
  id          String  @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  description String
  logoUrl     String?
  category    String
  tvl         Float?
  apy         Float?
  isActive    Boolean @default(true)

  @@map("protocols")
}

model DepositAddress {
  id            String   @id @default(auto()) @map("_id") @db.ObjectId
  userId        String   @db.ObjectId
  address       String   @unique
  tokenAddress  String   // RA token mint address
  network       String   // devnet, testnet, mainnet-beta
  isActive      Boolean  @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  user         User          @relation(fields: [userId], references: [id])
  transactions Transaction[]

  @@map("deposit_addresses")
}

model Transaction {
  id              String   @id @default(auto()) @map("_id") @db.ObjectId
  userId          String   @db.ObjectId
  depositAddressId String? @db.ObjectId
  signature       String   @unique
  fromAddress     String
  toAddress       String
  tokenAddress    String
  amount          Float
  decimals        Int      @default(9)
  blockTime       DateTime?
  slot            Int?
  confirmations   Int      @default(0)
  status          String   // pending, confirmed, failed
  type            String   // deposit, withdrawal, stake, unstake
  network         String
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  user           User            @relation(fields: [userId], references: [id])
  depositAddress DepositAddress? @relation(fields: [depositAddressId], references: [id])

  @@map("transactions")
}

model UserBalance {
  id           String   @id @default(auto()) @map("_id") @db.ObjectId
  userId       String   @db.ObjectId
  tokenAddress String
  balance      Float    @default(0)
  lockedBalance Float   @default(0) // Amount locked in staking
  network      String
  lastUpdated  DateTime @default(now())
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  user User @relation(fields: [userId], references: [id])

  @@unique([userId, tokenAddress, network])
  @@map("user_balances")
}
