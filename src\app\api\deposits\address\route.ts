import { NextResponse } from 'next/server'
import { Keypair, PublicKey } from '@solana/web3.js'
import { prisma } from '@/lib/prisma'

// Dynamic import to prevent build-time database access
async function getPrismaClient() {
  try {
    const { prisma } = await import('@/lib/prisma')
    return prisma
  } catch (error) {
    console.error('Failed to load Prisma client:', error)
    return null
  }
}

export async function POST(request: Request) {
  try {
    // Check if we're in build time (no database access needed)
    if (process.env.NODE_ENV === 'production' && !process.env.DATABASE_URL) {
      return NextResponse.json(
        { error: 'Database not available during build' },
        { status: 503 }
      )
    }

    const prisma = await getPrismaClient()
    if (!prisma) {
      return NextResponse.json(
        { error: 'Database connection failed' },
        { status: 503 }
      )
    }

    const body = await request.json()
    const { walletAddress, tokenAddress, network } = body

    // Validate required fields
    if (!walletAddress || !tokenAddress || !network) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Missing required fields: walletAddress, tokenAddress, network' 
        },
        { status: 400 }
      )
    }

    // Validate wallet address format
    try {
      new PublicKey(walletAddress)
    } catch (error) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid wallet address format' 
        },
        { status: 400 }
      )
    }

    // Validate token address format
    try {
      new PublicKey(tokenAddress)
    } catch (error) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid token address format' 
        },
        { status: 400 }
      )
    }

    // Validate network
    const validNetworks = ['devnet', 'testnet', 'mainnet-beta']
    if (!validNetworks.includes(network)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid network. Must be one of: devnet, testnet, mainnet-beta' 
        },
        { status: 400 }
      )
    }

    // Find or create user
    let user = await prisma.user.findUnique({
      where: { walletAddress }
    })

    if (!user) {
      user = await prisma.user.create({
        data: { walletAddress }
      })
    }

    // Check if deposit address already exists for this user, token, and network
    let depositAddress = await prisma.depositAddress.findFirst({
      where: {
        userId: user.id,
        tokenAddress,
        network,
        isActive: true
      }
    })

    if (!depositAddress) {
      // Generate a new unique deposit address
      // For this implementation, we'll create a derived address based on user wallet + token + network
      // In production, you might want to use a more sophisticated address generation scheme
      const seed = `${walletAddress}-${tokenAddress}-${network}-${Date.now()}`
      const keypair = Keypair.fromSeed(
        Buffer.from(seed).subarray(0, 32).fill(0, seed.length)
      )
      
      const newAddress = keypair.publicKey.toString()

      // Create new deposit address
      depositAddress = await prisma.depositAddress.create({
        data: {
          userId: user.id,
          address: newAddress,
          tokenAddress,
          network,
          isActive: true
        }
      })
    }

    return NextResponse.json({
      success: true,
      data: {
        id: depositAddress.id,
        userId: depositAddress.userId,
        address: depositAddress.address,
        tokenAddress: depositAddress.tokenAddress,
        network: depositAddress.network,
        isActive: depositAddress.isActive,
        createdAt: depositAddress.createdAt.toISOString(),
        updatedAt: depositAddress.updatedAt.toISOString()
      },
      message: 'Deposit address retrieved successfully',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error in deposit address endpoint:', error)
    
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error occurred',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json(
    { 
      success: false,
      error: 'Method not allowed. Use POST to get deposit address.' 
    },
    { status: 405 }
  )
}
