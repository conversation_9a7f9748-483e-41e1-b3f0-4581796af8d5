import { NextResponse } from 'next/server'
import { Connection, PublicKey } from '@solana/web3.js'

// Dynamic import to prevent build-time database access
async function getPrismaClient() {
  try {
    const { prisma } = await import('@/lib/prisma')
    return prisma
  } catch (error) {
    console.error('Failed to load Prisma client:', error)
    return null
  }
}

export async function POST(request: Request) {
  try {
    // Check if we're in build time (no database access needed)
    if (process.env.NODE_ENV === 'production' && !process.env.DATABASE_URL) {
      return NextResponse.json(
        { error: 'Database not available during build' },
        { status: 503 }
      )
    }

    const prisma = await getPrismaClient()
    if (!prisma) {
      return NextResponse.json(
        { error: 'Database connection failed' },
        { status: 503 }
      )
    }

    const body = await request.json()
    const { signature, walletAddress, expectedAmount } = body

    // Validate required fields
    if (!signature || !walletAddress) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Missing required fields: signature, walletAddress' 
        },
        { status: 400 }
      )
    }

    // Validate wallet address format
    try {
      new PublicKey(walletAddress)
    } catch (error) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid wallet address format' 
        },
        { status: 400 }
      )
    }

    // Find user
    const user = await prisma.user.findUnique({
      where: { walletAddress }
    })

    if (!user) {
      return NextResponse.json(
        { 
          success: false,
          error: 'User not found' 
        },
        { status: 404 }
      )
    }

    // Check if transaction already exists
    let transaction = await prisma.transaction.findUnique({
      where: { signature }
    })

    if (transaction) {
      return NextResponse.json({
        success: true,
        data: {
          transaction: {
            id: transaction.id,
            userId: transaction.userId,
            depositAddressId: transaction.depositAddressId,
            signature: transaction.signature,
            fromAddress: transaction.fromAddress,
            toAddress: transaction.toAddress,
            tokenAddress: transaction.tokenAddress,
            amount: transaction.amount,
            decimals: transaction.decimals,
            blockTime: transaction.blockTime?.toISOString(),
            slot: transaction.slot,
            confirmations: transaction.confirmations,
            status: transaction.status,
            type: transaction.type,
            network: transaction.network,
            createdAt: transaction.createdAt.toISOString(),
            updatedAt: transaction.updatedAt.toISOString()
          },
          confirmed: transaction.status === 'confirmed'
        },
        message: 'Transaction found in database',
        timestamp: new Date().toISOString()
      })
    }

    // Get network from environment
    const network = process.env.NEXT_PUBLIC_SOLANA_NETWORK || 'devnet'
    
    // Get RPC endpoint based on network
    let rpcEndpoint: string
    switch (network) {
      case 'devnet':
        rpcEndpoint = process.env.NEXT_PUBLIC_RPC_ENDPOINT || 'https://api.devnet.solana.com'
        break
      case 'testnet':
        rpcEndpoint = 'https://api.testnet.solana.com'
        break
      case 'mainnet-beta':
        rpcEndpoint = process.env.NEXT_PUBLIC_RPC_ENDPOINT || 'https://api.mainnet-beta.solana.com'
        break
      default:
        rpcEndpoint = 'https://api.devnet.solana.com'
    }

    // Create connection to Solana
    const connection = new Connection(rpcEndpoint, 'confirmed')

    try {
      // Get transaction details from blockchain
      const txInfo = await connection.getTransaction(signature, {
        commitment: 'confirmed',
        maxSupportedTransactionVersion: 0
      })

      if (!txInfo) {
        // Transaction not found on blockchain yet
        return NextResponse.json({
          success: true,
          data: {
            transaction: null,
            confirmed: false
          },
          message: 'Transaction not found on blockchain',
          timestamp: new Date().toISOString()
        })
      }

      // Extract transaction details
      const { meta, transaction: tx, blockTime, slot } = txInfo
      
      if (!meta || meta.err) {
        // Transaction failed
        return NextResponse.json({
          success: true,
          data: {
            transaction: null,
            confirmed: false
          },
          message: 'Transaction failed on blockchain',
          timestamp: new Date().toISOString()
        })
      }

      // Parse transaction for token transfers
      // This is a simplified version - in production you'd want more robust parsing
      const fromAddress = tx.message.accountKeys[0].toString()
      const toAddress = walletAddress // Assuming deposit to user's wallet
      const tokenAddress = process.env.NEXT_PUBLIC_SOLERA_RA_TOKEN_ADDRESS || '4aQNjPD9Zy9B86csxdXjUZHF4MpLWEM2z5psTyzDrQsQ'
      
      // For this example, we'll use the expected amount or default to 0
      const amount = expectedAmount || 0
      const decimals = 9

      // Create transaction record
      transaction = await prisma.transaction.create({
        data: {
          userId: user.id,
          signature,
          fromAddress,
          toAddress,
          tokenAddress,
          amount,
          decimals,
          blockTime: blockTime ? new Date(blockTime * 1000) : null,
          slot: slot || null,
          confirmations: 1, // Confirmed transaction
          status: 'confirmed',
          type: 'deposit',
          network
        }
      })

      return NextResponse.json({
        success: true,
        data: {
          transaction: {
            id: transaction.id,
            userId: transaction.userId,
            depositAddressId: transaction.depositAddressId,
            signature: transaction.signature,
            fromAddress: transaction.fromAddress,
            toAddress: transaction.toAddress,
            tokenAddress: transaction.tokenAddress,
            amount: transaction.amount,
            decimals: transaction.decimals,
            blockTime: transaction.blockTime?.toISOString(),
            slot: transaction.slot,
            confirmations: transaction.confirmations,
            status: transaction.status,
            type: transaction.type,
            network: transaction.network,
            createdAt: transaction.createdAt.toISOString(),
            updatedAt: transaction.updatedAt.toISOString()
          },
          confirmed: true
        },
        message: 'Transaction confirmed and recorded',
        timestamp: new Date().toISOString()
      })

    } catch (error) {
      console.error('Error fetching transaction from blockchain:', error)
      
      return NextResponse.json({
        success: false,
        error: 'Failed to fetch transaction from blockchain',
        message: error instanceof Error ? error.message : 'Unknown error occurred',
        timestamp: new Date().toISOString()
      }, { status: 500 })
    }

  } catch (error) {
    console.error('Error in transaction monitoring endpoint:', error)
    
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error occurred',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json(
    { 
      success: false,
      error: 'Method not allowed. Use POST to monitor transaction.' 
    },
    { status: 405 }
  )
}
