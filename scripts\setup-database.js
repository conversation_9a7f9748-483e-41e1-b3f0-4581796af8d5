#!/usr/bin/env node

/**
 * Database Setup Script for Solera Staking
 * 
 * This script helps set up the MongoDB database for the Solera Staking application.
 * It creates the necessary collections and indexes based on the Prisma schema.
 */

const { MongoClient } = require('mongodb')
require('dotenv').config({ path: '.env.local' })

const DATABASE_URL = process.env.DATABASE_URL || 'mongodb://localhost:27017/solera-staking'

async function setupDatabase() {
  console.log('🚀 Setting up Solera Staking database...')
  console.log('📍 Database URL:', DATABASE_URL.replace(/\/\/.*@/, '//***:***@'))

  let client
  
  try {
    // Connect to MongoDB
    client = new MongoClient(DATABASE_URL)
    await client.connect()
    console.log('✅ Connected to MongoDB')

    const db = client.db()
    
    // Create collections if they don't exist
    const collections = [
      'users',
      'tokens', 
      'staking_pools',
      'staking_positions',
      'protocols',
      'deposit_addresses',
      'transactions',
      'user_balances'
    ]

    for (const collectionName of collections) {
      try {
        await db.createCollection(collectionName)
        console.log(`✅ Created collection: ${collectionName}`)
      } catch (error) {
        if (error.code === 48) {
          console.log(`ℹ️  Collection already exists: ${collectionName}`)
        } else {
          console.error(`❌ Error creating collection ${collectionName}:`, error.message)
        }
      }
    }

    // Create indexes for better performance
    console.log('📊 Creating indexes...')

    // Users collection indexes
    await db.collection('users').createIndex({ walletAddress: 1 }, { unique: true })
    console.log('✅ Created unique index on users.walletAddress')

    // Tokens collection indexes
    await db.collection('tokens').createIndex({ symbol: 1 }, { unique: true })
    await db.collection('tokens').createIndex({ mintAddress: 1 }, { unique: true })
    console.log('✅ Created indexes on tokens collection')

    // Deposit addresses collection indexes
    await db.collection('deposit_addresses').createIndex({ address: 1 }, { unique: true })
    await db.collection('deposit_addresses').createIndex({ userId: 1, tokenAddress: 1, network: 1 })
    await db.collection('deposit_addresses').createIndex({ isActive: 1 })
    console.log('✅ Created indexes on deposit_addresses collection')

    // Transactions collection indexes
    await db.collection('transactions').createIndex({ signature: 1 }, { unique: true })
    await db.collection('transactions').createIndex({ userId: 1, createdAt: -1 })
    await db.collection('transactions').createIndex({ status: 1 })
    await db.collection('transactions').createIndex({ type: 1 })
    await db.collection('transactions').createIndex({ tokenAddress: 1 })
    console.log('✅ Created indexes on transactions collection')

    // User balances collection indexes
    await db.collection('user_balances').createIndex(
      { userId: 1, tokenAddress: 1, network: 1 }, 
      { unique: true }
    )
    await db.collection('user_balances').createIndex({ userId: 1 })
    console.log('✅ Created indexes on user_balances collection')

    // Staking positions collection indexes
    await db.collection('staking_positions').createIndex({ userId: 1 })
    await db.collection('staking_positions').createIndex({ tokenId: 1 })
    await db.collection('staking_positions').createIndex({ poolId: 1 })
    await db.collection('staking_positions').createIndex({ isActive: 1 })
    console.log('✅ Created indexes on staking_positions collection')

    // Insert default RA token if it doesn't exist
    const raTokenDevnet = {
      symbol: 'RA',
      name: 'Solera RA Token',
      logoUrl: null,
      decimals: 9,
      mintAddress: '4aQNjPD9Zy9B86csxdXjUZHF4MpLWEM2z5psTyzDrQsQ'
    }

    const raTokenMainnet = {
      symbol: 'RA_MAINNET',
      name: 'Solera RA Token (Mainnet)',
      logoUrl: null,
      decimals: 9,
      mintAddress: '2jPF5RY4B3jtJb4iAwRZ5J68WLLu4uaaBZ4wpjV29YYA'
    }

    try {
      await db.collection('tokens').insertOne(raTokenDevnet)
      console.log('✅ Inserted RA token (Devnet) into tokens collection')
    } catch (error) {
      if (error.code === 11000) {
        console.log('ℹ️  RA token (Devnet) already exists in tokens collection')
      } else {
        console.error('❌ Error inserting RA token (Devnet):', error.message)
      }
    }

    try {
      await db.collection('tokens').insertOne(raTokenMainnet)
      console.log('✅ Inserted RA token (Mainnet) into tokens collection')
    } catch (error) {
      if (error.code === 11000) {
        console.log('ℹ️  RA token (Mainnet) already exists in tokens collection')
      } else {
        console.error('❌ Error inserting RA token (Mainnet):', error.message)
      }
    }

    console.log('🎉 Database setup completed successfully!')
    console.log('')
    console.log('Next steps:')
    console.log('1. Start the development server: npm run dev')
    console.log('2. Connect a Solana wallet in the application')
    console.log('3. Test the deposit functionality')

  } catch (error) {
    console.error('❌ Database setup failed:', error)
    process.exit(1)
  } finally {
    if (client) {
      await client.close()
      console.log('🔌 Disconnected from MongoDB')
    }
  }
}

// Run the setup
if (require.main === module) {
  setupDatabase()
    .then(() => {
      console.log('✅ Setup script completed')
      process.exit(0)
    })
    .catch((error) => {
      console.error('❌ Setup script failed:', error)
      process.exit(1)
    })
}

module.exports = { setupDatabase }
