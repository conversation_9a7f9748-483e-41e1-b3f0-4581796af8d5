// API Services for Solera Staking Application
import { apiClient } from '@/lib/api'
import {
  UserProfile,
  WalletConnectionEvent,
  WalletConnectionResponse,
  AnalyticsEvent,
  AnalyticsResponse,
  TokenBalanceRequest,
  TokenBalanceResponse,
  StakingPositionsResponse,
  ApiResponse,
  DepositAddressRequest,
  DepositAddressResponse,
  TransactionHistoryRequest,
  TransactionHistoryResponse,
  BalanceUpdateRequest,
  BalanceUpdateResponse,
  MonitorTransactionRequest,
  MonitorTransactionResponse
} from '@/types/api'

/**
 * User Profile API Service
 */
export class UserService {
  /**
   * Get user profile by wallet address
   */
  static async getProfile(walletAddress: string): Promise<UserProfile> {
    try {
      console.log('👤 Fetching user profile for:', `${walletAddress.slice(0, 8)}...`)

      const response = await apiClient.get<UserProfile>(`/users/profile/${walletAddress}`)

      if (response.success && response.data) {
        console.log('✅ User profile fetched successfully')
        return response.data
      }

      throw new Error(response.message || 'Failed to fetch user profile')
    } catch (error) {
      console.error('❌ Error fetching user profile:', error)
      throw error
    }
  }

  /**
   * Update user profile
   */
  static async updateProfile(walletAddress: string, updates: Partial<UserProfile>): Promise<UserProfile> {
    try {
      console.log('📝 Updating user profile for:', `${walletAddress.slice(0, 8)}...`)

      const response = await apiClient.put<UserProfile>(`/users/profile/${walletAddress}`, updates)

      if (response.success && response.data) {
        console.log('✅ User profile updated successfully')
        return response.data
      }

      throw new Error(response.message || 'Failed to update user profile')
    } catch (error) {
      console.error('❌ Error updating user profile:', error)
      throw error
    }
  }

  /**
   * Create new user profile
   */
  static async createProfile(walletAddress: string, profileData: Partial<UserProfile>): Promise<UserProfile> {
    try {
      console.log('🆕 Creating user profile for:', `${walletAddress.slice(0, 8)}...`)

      const response = await apiClient.post<UserProfile>('/users/profile', {
        walletAddress,
        ...profileData
      })

      if (response.success && response.data) {
        console.log('✅ User profile created successfully')
        return response.data
      }

      throw new Error(response.message || 'Failed to create user profile')
    } catch (error) {
      console.error('❌ Error creating user profile:', error)
      throw error
    }
  }
}

/**
 * Wallet Connection API Service
 */
export class WalletService {
  /**
   * Record wallet connection event
   */
  static async recordConnection(connectionData: WalletConnectionEvent): Promise<WalletConnectionResponse> {
    try {
      console.log('🔗 Recording wallet connection:', {
        address: `${connectionData.walletAddress.slice(0, 8)}...`,
        type: connectionData.walletType,
        network: connectionData.network
      })

      const response = await apiClient.post<WalletConnectionResponse['data']>('/wallet/connect', connectionData)

      if (response.success && response.data) {
        console.log('✅ Wallet connection recorded successfully')
        return response as WalletConnectionResponse
      }

      throw new Error(response.message || 'Failed to record wallet connection')
    } catch (error) {
      console.error('❌ Error recording wallet connection:', error)
      throw error
    }
  }

  /**
   * Record wallet disconnection event
   */
  static async recordDisconnection(walletAddress: string): Promise<ApiResponse> {
    try {
      console.log('🔌 Recording wallet disconnection:', `${walletAddress.slice(0, 8)}...`)

      const response = await apiClient.post('/wallet/disconnect', { walletAddress })

      if (response.success) {
        console.log('✅ Wallet disconnection recorded successfully')
        return response
      }

      throw new Error(response.message || 'Failed to record wallet disconnection')
    } catch (error) {
      console.error('❌ Error recording wallet disconnection:', error)
      throw error
    }
  }

  /**
   * Get wallet session info
   */
  static async getSessionInfo(walletAddress: string): Promise<any> {
    try {
      console.log('📊 Fetching wallet session info:', `${walletAddress.slice(0, 8)}...`)

      const response = await apiClient.get(`/wallet/session/${walletAddress}`)

      if (response.success && response.data) {
        console.log('✅ Wallet session info fetched successfully')
        return response.data
      }

      throw new Error(response.message || 'Failed to fetch wallet session info')
    } catch (error) {
      console.error('❌ Error fetching wallet session info:', error)
      throw error
    }
  }
}

/**
 * Analytics API Service
 */
export class AnalyticsService {
  /**
   * Track user event
   */
  static async trackEvent(event: AnalyticsEvent): Promise<AnalyticsResponse> {
    try {
      console.log('📈 Tracking analytics event:', {
        event: event.event,
        userId: event.userId ? `${event.userId.slice(0, 8)}...` : 'anonymous',
        properties: Object.keys(event.properties || {})
      })

      const response = await apiClient.post<AnalyticsResponse['data']>('/analytics/track', event)

      if (response.success && response.data) {
        console.log('✅ Analytics event tracked successfully')
        return response as AnalyticsResponse
      }

      throw new Error(response.message || 'Failed to track analytics event')
    } catch (error) {
      // Silently handle analytics errors - don't spam console
      console.log('ℹ️ Analytics tracking unavailable, continuing...')
      return {
        success: false,
        message: 'Analytics tracking failed',
        timestamp: new Date().toISOString(),
        data: { eventId: '', processed: false }
      }
    }
  }

  /**
   * Track page view
   */
  static async trackPageView(page: string, userId?: string): Promise<void> {
    await this.trackEvent({
      event: 'page_view',
      properties: { page },
      userId,
      timestamp: new Date().toISOString()
    })
  }

  /**
   * Track wallet connection
   */
  static async trackWalletConnection(walletAddress: string, walletType: string): Promise<void> {
    await this.trackEvent({
      event: 'wallet_connected',
      properties: {
        walletType,
        network: process.env.NEXT_PUBLIC_SOLANA_NETWORK || 'unknown'
      },
      userId: walletAddress,
      timestamp: new Date().toISOString()
    })
  }

  /**
   * Track token balance fetch
   */
  static async trackTokenBalanceFetch(walletAddress: string, success: boolean, balance?: number): Promise<void> {
    await this.trackEvent({
      event: 'token_balance_fetched',
      properties: {
        success,
        balance: success ? balance : undefined,
        network: process.env.NEXT_PUBLIC_SOLANA_NETWORK || 'unknown'
      },
      userId: walletAddress,
      timestamp: new Date().toISOString()
    })
  }
}

/**
 * Token API Service
 */
export class TokenService {
  /**
   * Get enhanced token balance with metadata
   */
  static async getTokenBalance(request: TokenBalanceRequest): Promise<TokenBalanceResponse> {
    try {
      console.log('💰 Fetching enhanced token balance:', {
        wallet: `${request.walletAddress.slice(0, 8)}...`,
        token: `${request.tokenAddress.slice(0, 8)}...`,
        network: request.network
      })

      const response = await apiClient.post<TokenBalanceResponse['data']>('/tokens/balance', request)

      if (response.success && response.data) {
        console.log('✅ Enhanced token balance fetched successfully')
        return response as TokenBalanceResponse
      }

      throw new Error(response.message || 'Failed to fetch token balance')
    } catch (error) {
      console.error('❌ Error fetching enhanced token balance:', error)
      throw error
    }
  }

  /**
   * Get token metadata
   */
  static async getTokenMetadata(tokenAddress: string): Promise<any> {
    try {
      console.log('📋 Fetching token metadata:', `${tokenAddress.slice(0, 8)}...`)

      const response = await apiClient.get(`/tokens/metadata/${tokenAddress}`)

      if (response.success && response.data) {
        console.log('✅ Token metadata fetched successfully')
        return response.data
      }

      throw new Error(response.message || 'Failed to fetch token metadata')
    } catch (error) {
      console.error('❌ Error fetching token metadata:', error)
      throw error
    }
  }
}

/**
 * Staking API Service
 */
export class StakingService {
  /**
   * Get user staking positions
   */
  static async getStakingPositions(walletAddress: string, page = 1, limit = 10): Promise<StakingPositionsResponse> {
    try {
      console.log('🏦 Fetching staking positions:', {
        wallet: `${walletAddress.slice(0, 8)}...`,
        page,
        limit
      })

      const response = await apiClient.get<StakingPositionsResponse['data']>(`/staking/positions/${walletAddress}`, {
        params: { page, limit }
      })

      if (response.success && response.data) {
        console.log('✅ Staking positions fetched successfully')
        return response as StakingPositionsResponse
      }

      throw new Error(response.message || 'Failed to fetch staking positions')
    } catch (error) {
      console.error('❌ Error fetching staking positions:', error)
      throw error
    }
  }
}

/**
 * Deposit API Service
 */
export class DepositService {
  /**
   * Get or create deposit address for user
   */
  static async getDepositAddress(request: DepositAddressRequest): Promise<DepositAddressResponse> {
    try {
      console.log('🏦 Getting deposit address:', {
        wallet: `${request.walletAddress.slice(0, 8)}...`,
        token: `${request.tokenAddress.slice(0, 8)}...`,
        network: request.network
      })

      const response = await apiClient.post<DepositAddressResponse['data']>('/deposits/address', request)

      if (response.success && response.data) {
        console.log('✅ Deposit address retrieved successfully')
        return response as DepositAddressResponse
      }

      throw new Error(response.message || 'Failed to get deposit address')
    } catch (error) {
      console.error('❌ Error getting deposit address:', error)
      throw error
    }
  }

  /**
   * Get transaction history
   */
  static async getTransactionHistory(request: TransactionHistoryRequest): Promise<TransactionHistoryResponse> {
    try {
      console.log('📜 Fetching transaction history:', {
        wallet: `${request.walletAddress.slice(0, 8)}...`,
        type: request.type,
        limit: request.limit
      })

      const response = await apiClient.get<TransactionHistoryResponse['data']>('/deposits/history', {
        params: request
      })

      if (response.success && response.data) {
        console.log('✅ Transaction history fetched successfully')
        return response as TransactionHistoryResponse
      }

      throw new Error(response.message || 'Failed to fetch transaction history')
    } catch (error) {
      console.error('❌ Error fetching transaction history:', error)
      throw error
    }
  }

  /**
   * Update user balance
   */
  static async updateBalance(request: BalanceUpdateRequest): Promise<BalanceUpdateResponse> {
    try {
      console.log('💰 Updating user balance:', {
        wallet: `${request.walletAddress.slice(0, 8)}...`,
        token: `${request.tokenAddress.slice(0, 8)}...`,
        network: request.network
      })

      const response = await apiClient.post<BalanceUpdateResponse['data']>('/deposits/balance', request)

      if (response.success && response.data) {
        console.log('✅ User balance updated successfully')
        return response as BalanceUpdateResponse
      }

      throw new Error(response.message || 'Failed to update balance')
    } catch (error) {
      console.error('❌ Error updating balance:', error)
      throw error
    }
  }

  /**
   * Monitor transaction status
   */
  static async monitorTransaction(request: MonitorTransactionRequest): Promise<MonitorTransactionResponse> {
    try {
      console.log('🔍 Monitoring transaction:', {
        signature: `${request.signature.slice(0, 8)}...`,
        wallet: `${request.walletAddress.slice(0, 8)}...`
      })

      const response = await apiClient.post<MonitorTransactionResponse['data']>('/deposits/monitor', request)

      if (response.success && response.data) {
        console.log('✅ Transaction monitoring completed')
        return response as MonitorTransactionResponse
      }

      throw new Error(response.message || 'Failed to monitor transaction')
    } catch (error) {
      console.error('❌ Error monitoring transaction:', error)
      throw error
    }
  }
}

// All services are exported above with their class declarations
