'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useWallet, useConnection } from '@solana/wallet-adapter-react'
import { WalletMultiButton } from '@solana/wallet-adapter-react-ui'
import { useEffect, useState, useRef } from 'react'
import { PublicKey } from '@solana/web3.js'
import { getAssociatedTokenAddress, getAccount, getMint } from '@solana/spl-token'
import { User, Menu, X, Copy } from 'lucide-react'
import { apiClient } from '@/lib/api'
import { UserService, WalletService, AnalyticsService } from '@/services/api'
import { UserProfile, ApiErrorType } from '@/types/api'
// import { DepositModal } from '@/components/ui/DepositModal'

// Solera RA token contract address - get from environment or use default
const SOLERA_RA_TOKEN_ADDRESS = process.env.NEXT_PUBLIC_SOLERA_RA_TOKEN_ADDRESS || '2jPF5RY4B3jtJb4iAwRZ5J68WLLu4uaaBZ4wpjV29YYA'

// Development mode flag
const DEV_MODE = process.env.NEXT_PUBLIC_DEV_MODE === 'true'

// Mock token balance for development/testing
const MOCK_TOKEN_BALANCE = 1234.56

// Navigation data
const navigation = [
  { name: 'Dashboard', href: '/' },
  { name: 'Assets', href: '/assets' },
  { name: 'Planning', href: '/planning' },
  { name: 'Docs', href: 'https://docs.solera.work', external: true },
]

export function WalletButton() {
  const pathname = usePathname()
  const { connected, publicKey, disconnect } = useWallet()
  const { connection } = useConnection()
  const [tokenBalance, setTokenBalance] = useState<number | null>(null)
  const [isLoadingBalance, setIsLoadingBalance] = useState(false)
  const [isProfileDropdownOpen, setIsProfileDropdownOpen] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [isCopied, setIsCopied] = useState(false)
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null)
  const [isLoadingProfile, setIsLoadingProfile] = useState(false)
  const [apiError, setApiError] = useState<string | null>(null)
  const [isDepositModalOpen, setIsDepositModalOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const mobileMenuRef = useRef<HTMLDivElement>(null)

  // Log configuration on component mount
  useEffect(() => {
    console.log('=== WalletButton Configuration ===')
    console.log('Environment Variables:')
    console.log('- NEXT_PUBLIC_SOLANA_NETWORK:', process.env.NEXT_PUBLIC_SOLANA_NETWORK)
    console.log('- NEXT_PUBLIC_RPC_ENDPOINT:', process.env.NEXT_PUBLIC_RPC_ENDPOINT)
    console.log('- NEXT_PUBLIC_SOLERA_RA_TOKEN_ADDRESS:', process.env.NEXT_PUBLIC_SOLERA_RA_TOKEN_ADDRESS)
    console.log('- NEXT_PUBLIC_DEV_MODE:', process.env.NEXT_PUBLIC_DEV_MODE)
    console.log('- NEXT_PUBLIC_API_BASE_URL:', process.env.NEXT_PUBLIC_API_BASE_URL)
    console.log('Resolved Values:')
    console.log('- Token Address:', SOLERA_RA_TOKEN_ADDRESS)
    console.log('- Development Mode:', DEV_MODE)
    console.log('- Connection RPC Endpoint:', connection.rpcEndpoint)
    console.log('- API Base URL:', apiClient.getBaseURL())
    console.log('=== Ready to fetch REAL token balances from Devnet ===')
  }, [])

  // Fetch user profile when wallet connects
  useEffect(() => {
    const fetchUserProfile = async () => {
      if (!connected || !publicKey) {
        setUserProfile(null)
        setApiError(null)
        apiClient.setWalletAddress(null)
        return
      }

      const walletAddress = publicKey.toString()
      apiClient.setWalletAddress(walletAddress)
      setIsLoadingProfile(true)
      setApiError(null)

      try {
        // Track wallet connection analytics (non-blocking)
        AnalyticsService.trackWalletConnection(walletAddress, 'unknown')
          .catch(() => {}) // Silently ignore analytics failures

        // Record wallet connection event (non-blocking)
        WalletService.recordConnection({
          walletAddress,
          walletType: 'unknown', // Could be enhanced to detect wallet type
          network: process.env.NEXT_PUBLIC_SOLANA_NETWORK || 'devnet',
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent
        }).catch(() => {}) // Silently ignore connection recording failures

        // Fetch user profile (this can fail gracefully)
        try {
          const profile = await UserService.getProfile(walletAddress)
          setUserProfile(profile)
          console.log('✅ User profile loaded successfully')
        } catch (profileError) {
          console.log('ℹ️ User profile not found, continuing with basic functionality')
          setApiError('Profile unavailable')
        }
      } catch (error) {
        console.log('ℹ️ API services unavailable, continuing with basic wallet functionality')
        setApiError('API unavailable')
      } finally {
        setIsLoadingProfile(false)
      }
    }

    fetchUserProfile()
  }, [connected, publicKey])

  // Validate and determine network configuration
  const getNetworkInfo = () => {
    const envNetwork = process.env.NEXT_PUBLIC_SOLANA_NETWORK
    const rpcEndpoint = connection.rpcEndpoint

    console.log('Network Configuration:')
    console.log('- Environment Network:', envNetwork)
    console.log('- RPC Endpoint:', rpcEndpoint)
    console.log('- Development Mode:', DEV_MODE)

    const isMainnet = envNetwork === 'mainnet-beta' || envNetwork === 'mainnet'
    const isDevnet = envNetwork === 'devnet'
    const isTestnet = envNetwork === 'testnet'

    // Check RPC endpoint consistency
    const rpcMatchesNetwork =
      (isMainnet && (rpcEndpoint.includes('mainnet') || rpcEndpoint.includes('projectserum') || rpcEndpoint.includes('ankr.com/solana'))) ||
      (isDevnet && rpcEndpoint.includes('devnet')) ||
      (isTestnet && rpcEndpoint.includes('testnet'))

    console.log('- Network Type:', { isMainnet, isDevnet, isTestnet })
    console.log('- RPC Matches Network:', rpcMatchesNetwork)

    return {
      isMainnet,
      isDevnet,
      isTestnet,
      rpcMatchesNetwork,
      canFetchRealToken: rpcMatchesNetwork, // Can fetch real tokens on any network if RPC matches
      shouldUseMockData: DEV_MODE && !rpcMatchesNetwork // Only use mock data if DEV_MODE is true AND network mismatch
    }
  }

  // Fetch Solera RA token balance with retry logic
  useEffect(() => {
    const fetchTokenBalance = async (retryCount = 0) => {
      if (!connected || !publicKey) {
        console.log('Wallet not connected or no public key available')
        setTokenBalance(null)
        return
      }

      console.log('=== Starting Token Balance Fetch ===')
      console.log('Wallet Connected:', connected)
      console.log('Public Key:', publicKey.toString())
      console.log('Token Address:', SOLERA_RA_TOKEN_ADDRESS)
      console.log('Connection Endpoint:', connection.rpcEndpoint)
      console.log('Retry Count:', retryCount)

      // Get network configuration
      const networkInfo = getNetworkInfo()

      setIsLoadingBalance(true)

      // Handle different network scenarios
      if (networkInfo.shouldUseMockData) {
        console.log('🔧 Using mock data for development/testing')
        console.log('Mock Balance:', MOCK_TOKEN_BALANCE)

        // Simulate network delay for realistic testing
        setTimeout(() => {
          setTokenBalance(MOCK_TOKEN_BALANCE)
          setIsLoadingBalance(false)
          console.log('=== Mock Token Balance Set SUCCESS ===')
        }, 1000)
        return
      }

      if (!networkInfo.canFetchRealToken) {
        console.warn('⚠️ Network/RPC mismatch detected. Cannot fetch token balance.')
        console.log('Network Info:', networkInfo)
        console.log('Expected: RPC endpoint should match the configured network')
        setTokenBalance(null) // Show error state for network mismatch
        setIsLoadingBalance(false)
        return
      }

      try {
        console.log('🔗 Fetching real RA token balance from Devnet blockchain...')
        console.log('Token Address:', SOLERA_RA_TOKEN_ADDRESS)
        const tokenMint = new PublicKey(SOLERA_RA_TOKEN_ADDRESS)

        // Get token mint info to get the correct decimals
        const mintInfo = await getMint(connection, tokenMint)
        console.log('Solera RA Token Mint Info:', {
          address: SOLERA_RA_TOKEN_ADDRESS,
          decimals: mintInfo.decimals,
          supply: mintInfo.supply.toString()
        })

        const associatedTokenAddress = await getAssociatedTokenAddress(
          tokenMint,
          publicKey
        )
        console.log('Associated Token Address:', associatedTokenAddress.toString())

        const tokenAccount = await getAccount(connection, associatedTokenAddress)
        console.log('Token Account Info:', {
          amount: tokenAccount.amount.toString(),
          decimals: mintInfo.decimals
        })

        // Use the actual decimals from the mint
        const balance = Number(tokenAccount.amount) / Math.pow(10, mintInfo.decimals)
        console.log('Calculated Balance:', balance)
        console.log('=== Real Token Balance Fetch SUCCESS ===')
        setTokenBalance(balance)

        // Track successful token balance fetch (non-blocking)
        if (publicKey) {
          AnalyticsService.trackTokenBalanceFetch(publicKey.toString(), true, balance)
            .catch(() => {}) // Silently ignore analytics failures
        }
      } catch (error) {
        console.error('Error fetching token balance:', error)

        // Check if it's a "TokenAccountNotFoundError" (account doesn't exist)
        if (error instanceof Error) {
          if (error.message.includes('could not find account') ||
              error.message.includes('Invalid account owner') ||
              error.message.includes('Account does not exist')) {
            console.log('Token account does not exist - user has 0 balance')
            setTokenBalance(0)
          } else if (error.message.includes('Invalid public key input') ||
                     error.message.includes('Invalid mint')) {
            console.error('Invalid token mint address:', SOLERA_RA_TOKEN_ADDRESS)
            setTokenBalance(null)
          } else if ((error.message.includes('403') ||
                      error.message.includes('Access forbidden') ||
                      error.message.includes('Network request failed') ||
                      error.message.includes('fetch') ||
                      error.message.includes('timeout') ||
                      error.message.includes('ECONNRESET')) && retryCount < 2) {
            // Retry for RPC errors (403, network issues, timeouts) up to 2 times
            console.log(`🔄 RPC error detected, retrying... (attempt ${retryCount + 1}/3)`)
            console.log(`Error details: ${error.message}`)
            setTimeout(() => {
              fetchTokenBalance(retryCount + 1)
            }, Math.min(1000 * Math.pow(2, retryCount), 5000)) // Exponential backoff: 1s, 2s, max 5s
            return // Don't set loading to false yet
          } else {
            console.error('Unexpected error:', error.message)
            setTokenBalance(null)
          }
        } else {
          console.error('Unknown error type:', error)
          setTokenBalance(null)
        }

        // Track failed token balance fetch (non-blocking)
        if (publicKey) {
          AnalyticsService.trackTokenBalanceFetch(publicKey.toString(), false)
            .catch(() => {}) // Silently ignore analytics failures
        }
      } finally {
        setIsLoadingBalance(false)
      }
    }

    fetchTokenBalance()
  }, [connected, publicKey, connection])

  // Click outside handler for dropdowns
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsProfileDropdownOpen(false)
        setIsCopied(false) // Reset copy state when dropdown closes
      }
      if (mobileMenuRef.current && !mobileMenuRef.current.contains(event.target as Node)) {
        setIsMobileMenuOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // Format token balance for display
  const formatBalance = (balance: number | null): string => {
    if (balance === null) return '--'
    if (balance === 0) return '0.00'
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 6, // Show more decimals for small amounts
    }).format(balance)
  }

  // Format wallet address for display
  const formatWalletAddress = (address: string): string => {
    return `${address.slice(0, 4)}...${address.slice(-4)}`
  }

  // Handle wallet disconnect
  const handleDisconnect = async () => {
    try {
      const walletAddress = publicKey?.toString()

      // Record disconnection event if wallet was connected
      if (walletAddress) {
        try {
          await WalletService.recordDisconnection(walletAddress)
          console.log('✅ Wallet disconnection recorded')
        } catch (error) {
          console.log('ℹ️ Failed to record disconnection, continuing...')
        }
      }

      await disconnect()
      setIsProfileDropdownOpen(false)
      setTokenBalance(null)
      setIsCopied(false) // Reset copy state on disconnect
      setUserProfile(null)
      setIsLoadingProfile(false)
      setApiError(null)
      apiClient.setWalletAddress(null)

      console.log('🔌 Wallet disconnected and state cleared')
    } catch (error) {
      console.error('Error disconnecting wallet:', error)
    }
  }

  // Handle copy wallet address to clipboard
  const handleCopyAddress = async () => {
    if (!publicKey) return

    try {
      const address = publicKey.toString()
      await navigator.clipboard.writeText(address)
      console.log('Wallet address copied to clipboard:', address)

      // Show "Copied" feedback
      setIsCopied(true)

      // Reset to copy icon after 3 seconds
      setTimeout(() => {
        setIsCopied(false)
      }, 3000)
    } catch (error) {
      console.error('Failed to copy wallet address to clipboard:', error)
      // Fallback for older browsers or when clipboard API fails
      try {
        const textArea = document.createElement('textarea')
        textArea.value = publicKey.toString()
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)

        setIsCopied(true)
        setTimeout(() => {
          setIsCopied(false)
        }, 3000)
        console.log('Wallet address copied using fallback method')
      } catch (fallbackError) {
        console.error('Fallback copy method also failed:', fallbackError)
      }
    }
  }

  return (
    <>
      <div className="flex items-center space-x-3">
        {connected ? (
          /* Connected State: Token Balance + Profile Avatar */
          <div className="flex items-center space-x-3">
            {/* Token Balance - Hidden on mobile */}
            <div className="hidden sm:block text-xs text-gray-300 font-medium">
              {isLoadingBalance ? (
                <span className="animate-pulse">Loading...</span>
              ) : tokenBalance === null ? (
                <span className="text-red-400">Error</span>
              ) : (
                <span>{formatBalance(tokenBalance)} RA</span>
              )}
            </div>

            {/* Profile Avatar with Dropdown */}
            <div className="relative" ref={dropdownRef}>
              <button
                onClick={() => setIsProfileDropdownOpen(!isProfileDropdownOpen)}
                className="w-8 h-8 flex items-center justify-center cursor-pointer transition-all
                   md:hover:scale-105 md:transition-transform
                  bg-white rounded"
              >
                <User className="w-3.5 h-3.5 !text-black md:text-white" />
              </button>

            {/* Profile Dropdown */}
            {isProfileDropdownOpen && (
              <div className="dropdown-menu absolute right-0 top-10 w-64 py-2 z-50">
                {/* Account Balance Section */}
                <div className="px-4 py-3 border-b" style={{ borderColor: 'var(--border-primary)' }}>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>Account Balance</span>
                    <button
                      onClick={handleDisconnect}
                      className="text-xs text-blue-400 hover:text-blue-300 transition-colors"
                    >
                      Disconnect
                    </button>
                  </div>
                  <div className="text-lg font-semibold" style={{ color: 'var(--text-primary)' }}>$0.00</div>
                </div>

                {/* Solera Token Balance */}
                <div className="px-4 py-2 border-b" style={{ borderColor: 'var(--border-primary)' }}>
                  <div className="flex items-center justify-between">
                    <span className="text-sm" style={{ color: 'var(--text-primary)' }}>Solera</span>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm" style={{ color: 'var(--text-primary)' }}>RA {formatBalance(tokenBalance)}</span>
                      <div className="w-2 h-2 bg-blue-500"></div>
                    </div>
                  </div>
                </div>

                {/* Wallet Address */}
                {publicKey && (
                  <div className="px-4 py-2 border-b" style={{ borderColor: 'var(--border-primary)' }}>
                    <div className="text-xs mb-1" style={{ color: 'var(--text-secondary)' }}>Wallet</div>
                    <div className="flex items-center justify-between">
                      <div className="text-sm font-mono" style={{ color: 'var(--text-primary)' }}>
                        {formatWalletAddress(publicKey.toString())}
                      </div>
                      <button
                        onClick={handleCopyAddress}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault()
                            handleCopyAddress()
                          }
                        }}
                        className="ml-2 rounded-sm hover:opacity-50 transition-colors focus:outline-none focus:ring-0 focus:ring-blue-500 focus:ring-opacity-50"
                        aria-label={isCopied ? "Address copied" : "Copy wallet address"}
                        title={isCopied ? "Copied!" : "Copy wallet address"}
                        tabIndex={0}
                      >
                        {isCopied ? (
                          <span className="text-xs text-[--accent-blue] font-medium">Copied</span>
                        ) : (
                          <Copy className="w-3.5 h-3.5 hover:text-gray-300 transition-colors" style={{ color: 'var(--text-secondary)' }} />
                        )}
                      </button>
                    </div>
                  </div>
                )}

                {/* Menu Items */}
                <div className="py-1">
                  <Link
                    href="/"
                    className="block px-4 py-2 text-sm transition-colors hover:bg-gray-800"
                    style={{ color: 'var(--text-primary)' }}
                    onClick={() => setIsProfileDropdownOpen(false)}
                  >
                    Dashboard
                  </Link>
                  <button
                    onClick={() => {
                      // setIsDepositModalOpen(true)
                      setIsProfileDropdownOpen(false)
                      alert('Deposit functionality coming soon!')
                    }}
                    className="block w-full text-left px-4 py-2 text-sm transition-colors hover:bg-gray-800"
                    style={{ color: 'var(--text-primary)' }}
                  >
                    Deposit
                  </button>
                  <Link
                    href="/withdrawals"
                    className="block px-4 py-2 text-sm transition-colors hover:bg-gray-800"
                    style={{ color: 'var(--text-primary)' }}
                    onClick={() => setIsProfileDropdownOpen(false)}
                  >
                    Withdrawals
                  </Link>
                  <Link
                    href="/history"
                    className="block px-4 py-2 text-sm transition-colors hover:bg-gray-800"
                    style={{ color: 'var(--text-primary)' }}
                    onClick={() => setIsProfileDropdownOpen(false)}
                  >
                    Transaction History
                  </Link>
                </div>
              </div>
            )}
          </div>
        </div>
        ) : (
          /* Disconnected State: Connect Wallet Button */
          <WalletMultiButton>
            Connect
          </WalletMultiButton>
        )}

        {/* Mobile Menu Button */}
        <button
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          className="md:hidden w-8 h-8 bg-white rounded flex items-center justify-center hover:bg-gray-100 transition-colors"
        >
          {isMobileMenuOpen ? (
            <X className="w-4 h-4 text-black" />
          ) : (
            <Menu className="w-4 h-4 text-black" />
          )}
        </button>
      </div>

      {/* Mobile Menu */}
      {isMobileMenuOpen && (
        <div
          ref={mobileMenuRef}
          className="dropdown-menu md:hidden absolute top-16 left-4 right-4 py-2 z-40 rounded"
        >
          {/* Mobile Navigation */}
          <div className="py-2">
            {navigation.map((item) => {
              const isActive = pathname === item.href && !item.external

              if (item.external) {
                return (
                  <a
                    key={item.name}
                    href={item.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="block px-4 py-3 text-sm font-medium transition-colors hover:bg-gray-800"
                    style={{ color: 'var(--text-secondary)' }}
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    {item.name}
                  </a>
                )
              }

              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`block px-4 py-3 text-sm font-medium transition-colors hover:bg-gray-800 ${
                    isActive ? 'bg-gray-800' : ''
                  }`}
                  style={{
                    color: isActive ? 'var(--text-primary)' : 'var(--text-secondary)'
                  }}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  {item.name}
                </Link>
              )
            })}
          </div>

          {/* Mobile Token Balance (if connected) */}
          {connected && (
            <div className="border-t px-4 py-3" style={{ borderColor: 'var(--border-primary)' }}>
              <div className="text-xs mb-1" style={{ color: 'var(--text-secondary)' }}>Token Balance</div>
              <div className="text-sm" style={{ color: 'var(--text-primary)' }}>
                {isLoadingBalance ? (
                  <span className="animate-pulse">Loading...</span>
                ) : tokenBalance === null ? (
                  <span className="text-red-400">Error loading balance</span>
                ) : (
                  <span>{formatBalance(tokenBalance)} RA</span>
                )}
              </div>
            </div>
          )}
        </div>
      )}

    </>
  )
}
