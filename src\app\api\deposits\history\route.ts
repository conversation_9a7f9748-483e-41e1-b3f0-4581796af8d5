import { NextResponse } from 'next/server'
import { PublicKey } from '@solana/web3.js'

// Dynamic import to prevent build-time database access
async function getPrismaClient() {
  try {
    const { prisma } = await import('@/lib/prisma')
    return prisma
  } catch (error) {
    console.error('Failed to load Prisma client:', error)
    return null
  }
}

export async function GET(request: Request) {
  try {
    // Check if we're in build time (no database access needed)
    if (process.env.NODE_ENV === 'production' && !process.env.DATABASE_URL) {
      return NextResponse.json(
        { error: 'Database not available during build' },
        { status: 503 }
      )
    }

    const prisma = await getPrismaClient()
    if (!prisma) {
      return NextResponse.json(
        { error: 'Database connection failed' },
        { status: 503 }
      )
    }

    const { searchParams } = new URL(request.url)
    const walletAddress = searchParams.get('walletAddress')
    const tokenAddress = searchParams.get('tokenAddress')
    const type = searchParams.get('type')
    const limit = parseInt(searchParams.get('limit') || '10')
    const offset = parseInt(searchParams.get('offset') || '0')

    // Validate required fields
    if (!walletAddress) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Missing required field: walletAddress' 
        },
        { status: 400 }
      )
    }

    // Validate wallet address format
    try {
      new PublicKey(walletAddress)
    } catch (error) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid wallet address format' 
        },
        { status: 400 }
      )
    }

    // Validate limit and offset
    if (limit < 1 || limit > 100) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Limit must be between 1 and 100' 
        },
        { status: 400 }
      )
    }

    if (offset < 0) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Offset must be non-negative' 
        },
        { status: 400 }
      )
    }

    // Find user
    const user = await prisma.user.findUnique({
      where: { walletAddress }
    })

    if (!user) {
      return NextResponse.json({
        success: true,
        data: [],
        pagination: {
          page: Math.floor(offset / limit) + 1,
          limit,
          total: 0,
          totalPages: 0
        },
        message: 'No transactions found for this wallet',
        timestamp: new Date().toISOString()
      })
    }

    // Build where clause
    const whereClause: any = {
      userId: user.id
    }

    if (tokenAddress) {
      whereClause.tokenAddress = tokenAddress
    }

    if (type) {
      whereClause.type = type
    }

    // Get total count
    const total = await prisma.transaction.count({
      where: whereClause
    })

    // Get transactions
    const transactions = await prisma.transaction.findMany({
      where: whereClause,
      orderBy: {
        createdAt: 'desc'
      },
      skip: offset,
      take: limit,
      include: {
        depositAddress: true
      }
    })

    // Format transactions for response
    const formattedTransactions = transactions.map(tx => ({
      id: tx.id,
      userId: tx.userId,
      depositAddressId: tx.depositAddressId,
      signature: tx.signature,
      fromAddress: tx.fromAddress,
      toAddress: tx.toAddress,
      tokenAddress: tx.tokenAddress,
      amount: tx.amount,
      decimals: tx.decimals,
      blockTime: tx.blockTime?.toISOString(),
      slot: tx.slot,
      confirmations: tx.confirmations,
      status: tx.status,
      type: tx.type,
      network: tx.network,
      createdAt: tx.createdAt.toISOString(),
      updatedAt: tx.updatedAt.toISOString()
    }))

    const totalPages = Math.ceil(total / limit)
    const currentPage = Math.floor(offset / limit) + 1

    return NextResponse.json({
      success: true,
      data: formattedTransactions,
      pagination: {
        page: currentPage,
        limit,
        total,
        totalPages
      },
      message: 'Transaction history retrieved successfully',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error in transaction history endpoint:', error)
    
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error occurred',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}

export async function POST() {
  return NextResponse.json(
    { 
      success: false,
      error: 'Method not allowed. Use GET to retrieve transaction history.' 
    },
    { status: 405 }
  )
}
