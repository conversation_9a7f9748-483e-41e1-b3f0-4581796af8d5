import { NextResponse } from 'next/server'
import { Connection, PublicKey } from '@solana/web3.js'
import { getAssociatedTokenAddress, getAccount } from '@solana/spl-token'

// Dynamic import to prevent build-time database access
async function getPrismaClient() {
  try {
    const { prisma } = await import('@/lib/prisma')
    return prisma
  } catch (error) {
    console.error('Failed to load Prisma client:', error)
    return null
  }
}

export async function POST(request: Request) {
  try {
    // Check if we're in build time (no database access needed)
    if (process.env.NODE_ENV === 'production' && !process.env.DATABASE_URL) {
      return NextResponse.json(
        { error: 'Database not available during build' },
        { status: 503 }
      )
    }

    const prisma = await getPrismaClient()
    if (!prisma) {
      return NextResponse.json(
        { error: 'Database connection failed' },
        { status: 503 }
      )
    }

    const body = await request.json()
    const { walletAddress, tokenAddress, network } = body

    // Validate required fields
    if (!walletAddress || !tokenAddress || !network) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Missing required fields: walletAddress, tokenAddress, network' 
        },
        { status: 400 }
      )
    }

    // Validate wallet address format
    let walletPublicKey: PublicKey
    try {
      walletPublicKey = new PublicKey(walletAddress)
    } catch (error) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid wallet address format' 
        },
        { status: 400 }
      )
    }

    // Validate token address format
    let tokenPublicKey: PublicKey
    try {
      tokenPublicKey = new PublicKey(tokenAddress)
    } catch (error) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid token address format' 
        },
        { status: 400 }
      )
    }

    // Validate network
    const validNetworks = ['devnet', 'testnet', 'mainnet-beta']
    if (!validNetworks.includes(network)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid network. Must be one of: devnet, testnet, mainnet-beta' 
        },
        { status: 400 }
      )
    }

    // Find or create user
    let user = await prisma.user.findUnique({
      where: { walletAddress }
    })

    if (!user) {
      user = await prisma.user.create({
        data: { walletAddress }
      })
    }

    // Get RPC endpoint based on network
    let rpcEndpoint: string
    switch (network) {
      case 'devnet':
        rpcEndpoint = process.env.NEXT_PUBLIC_RPC_ENDPOINT || 'https://api.devnet.solana.com'
        break
      case 'testnet':
        rpcEndpoint = 'https://api.testnet.solana.com'
        break
      case 'mainnet-beta':
        rpcEndpoint = process.env.NEXT_PUBLIC_RPC_ENDPOINT || 'https://api.mainnet-beta.solana.com'
        break
      default:
        rpcEndpoint = 'https://api.devnet.solana.com'
    }

    // Create connection to Solana
    const connection = new Connection(rpcEndpoint, 'confirmed')

    let balance = 0
    let decimals = 9 // Default for most SPL tokens

    try {
      // Get associated token account address
      const associatedTokenAddress = await getAssociatedTokenAddress(
        tokenPublicKey,
        walletPublicKey
      )

      // Get token account info
      const tokenAccount = await getAccount(connection, associatedTokenAddress)
      
      // Convert balance from lamports to tokens
      balance = Number(tokenAccount.amount) / Math.pow(10, decimals)
      
    } catch (error) {
      // Token account might not exist yet (balance is 0)
      console.log('Token account not found or error fetching balance:', error)
      balance = 0
    }

    // Update or create user balance record
    const userBalance = await prisma.userBalance.upsert({
      where: {
        userId_tokenAddress_network: {
          userId: user.id,
          tokenAddress,
          network
        }
      },
      update: {
        balance,
        lastUpdated: new Date()
      },
      create: {
        userId: user.id,
        tokenAddress,
        balance,
        lockedBalance: 0,
        network,
        lastUpdated: new Date()
      }
    })

    return NextResponse.json({
      success: true,
      data: {
        id: userBalance.id,
        userId: userBalance.userId,
        tokenAddress: userBalance.tokenAddress,
        balance: userBalance.balance,
        lockedBalance: userBalance.lockedBalance,
        network: userBalance.network,
        lastUpdated: userBalance.lastUpdated.toISOString(),
        createdAt: userBalance.createdAt.toISOString(),
        updatedAt: userBalance.updatedAt.toISOString()
      },
      message: 'Balance updated successfully',
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error in balance update endpoint:', error)
    
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error occurred',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json(
    { 
      success: false,
      error: 'Method not allowed. Use POST to update balance.' 
    },
    { status: 405 }
  )
}
